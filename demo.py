#!/usr/bin/env python3
"""
Demo script showing how to use the SVG Resizer application.
This script demonstrates programmatic usage and testing.
"""

import os
import sys
import subprocess
import time

def create_sample_svg(filename, width=100, height=100):
    """Create a sample SVG file for testing"""
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{width}" height="{height}" viewBox="0 0 {width} {height}" xmlns="http://www.w3.org/2000/svg">
  <rect x="10" y="10" width="{width-20}" height="{height-20}" 
        fill="#3498db" stroke="#2c3e50" stroke-width="2" rx="5"/>
  <text x="{width//2}" y="{height//2}" text-anchor="middle" 
        font-family="Arial" font-size="12" fill="white">
    {width}×{height}
  </text>
</svg>'''
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(svg_content)
    print(f"Created sample SVG: {filename} ({width}×{height})")

def main():
    """Main demo function"""
    print("SVG Resizer Demo")
    print("=" * 50)
    
    # Create sample SVG files
    print("\n1. Creating sample SVG files...")
    create_sample_svg("demo_small.svg", 50, 50)
    create_sample_svg("demo_medium.svg", 200, 150)
    create_sample_svg("demo_large.svg", 400, 300)
    
    print("\n2. Available demo files:")
    for filename in ["demo_small.svg", "demo_medium.svg", "demo_large.svg", "test_logo.svg"]:
        if os.path.exists(filename):
            print(f"   - {filename}")
    
    print("\n3. Usage Instructions:")
    print("   a) Run the SVG Resizer application:")
    print("      python svgresize.py")
    print()
    print("   b) Try these features:")
    print("      - Drag any demo SVG file onto the application window")
    print("      - Use the scale slider to resize proportionally")
    print("      - Enter specific dimensions in the width/height boxes")
    print("      - Toggle aspect ratio lock on/off")
    print("      - Click 'Toggle Overlay Mode' to test transparency")
    print("      - Save your resized SVG with 'Save' or 'Save As'")
    
    print("\n4. Testing Workflow:")
    print("   - Load demo_small.svg (50×50)")
    print("   - Scale to 200% using slider (should become 100×100)")
    print("   - Switch to manual input: set width to 150")
    print("   - With aspect ratio locked, height should auto-adjust to 150")
    print("   - Save as 'demo_small_resized.svg'")
    
    print("\n5. Overlay Mode Testing:")
    print("   - Load any SVG file")
    print("   - Resize to desired dimensions")
    print("   - Click 'Toggle Overlay Mode'")
    print("   - Open a web browser")
    print("   - Position the transparent SVG over the browser")
    print("   - Click 'Exit Overlay Mode' to return to normal controls")
    
    # Ask if user wants to launch the application
    print("\n" + "=" * 50)
    response = input("Launch SVG Resizer now? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        print("Launching SVG Resizer...")
        try:
            # Launch the application
            subprocess.Popen([sys.executable, "svgresize.py"])
            print("Application launched! Try dragging one of the demo SVG files onto it.")
        except Exception as e:
            print(f"Error launching application: {e}")
            print("You can manually run: python svgresize.py")
    else:
        print("You can manually launch the application with: python svgresize.py")

if __name__ == "__main__":
    main()
