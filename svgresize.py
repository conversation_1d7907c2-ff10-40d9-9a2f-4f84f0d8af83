# pip install pyside6 pywin32
import sys, win32gui, win32con
from PySide6.QtCore    import Qt
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QSlider
)

SVG_PATH = "logo.svg"           # any SVG you want to resize

class SvgOverlay(QWidget):
    def __init__(self):
        super().__init__()

        # ---- window itself --------------------------------------------------
        self.setWindowFlags(
            Qt.FramelessWindowHint        |   # no chrome
            Qt.WindowStaysOnTopHint       |   # always on top
            Qt.WindowTransparentForInput      # click-through overlay
        )
        self.setAttribute(Qt.WA_TranslucentBackground)  # real alpha
        self.setMouseTracking(False)        # avoid accidental focus
        self.resize(400, 450)               # starter size

        # ---- UI widgets ------------------------------------------------------
        self.svg   = QSvgWidget(SVG_PATH)
        self.slider = QSlider(Qt.Horizontal, minimum=10, maximum=400, value=100)
        self.slider.valueChanged.connect(self.scale_svg)

        lay = QVBoxLayout(self)
        lay.addWidget(self.svg, 1)
        lay.addWidget(self.slider)

        # ---- make the *whole* window click-through --------------------------
        self._hwnd = int(self.winId())
        self._make_click_through()

    def scale_svg(self, value):             # slider callback
        factor = value / 100
        side   = int(400 * factor)
        self.svg.resize(side, side)

    # Win32 one-liner to add WS_EX_LAYERED|TRANSPARENT
    def _make_click_through(self):
        style = win32gui.GetWindowLong(self._hwnd, win32con.GWL_EXSTYLE)
        style |= win32con.WS_EX_LAYERED | win32con.WS_EX_TRANSPARENT
        win32gui.SetWindowLong(self._hwnd, win32con.GWL_EXSTYLE, style)
        win32gui.SetLayeredWindowAttributes(
            self._hwnd, 0, 255, win32con.LWA_ALPHA
        )

if __name__ == "__main__":
    app = QApplication(sys.argv)
    SvgOverlay().show()
    sys.exit(app.exec())
