# SVG Resizer - Comprehensive SVG Resizing Tool

A powerful GUI application for resizing SVG files with real-time preview and transparent overlay functionality. Perfect for web developers who need to quickly resize SVG graphics and test them in context.

## Features

### 🎯 Core Functionality
- **Drag & Drop Support**: Simply drag SVG files onto the application window
- **File Browser**: Use the "Open File" button to browse and select SVG files
- **Real-time Preview**: See changes instantly as you adjust dimensions
- **Transparent Overlay**: Position the SVG over web browsers or other applications
- **Aspect Ratio Control**: Lock/unlock aspect ratio for proportional or independent scaling

### 🎛️ Resizing Controls
- **Proportional Slider**: Scale from 10% to 500% while maintaining aspect ratio
- **Manual Input**: Enter exact width and height values in pixels
- **Live Updates**: All controls sync automatically for consistent sizing

### 💾 Save Options
- **Save**: Overwrite the original SVG file with new dimensions
- **Save As**: Create a new resized SVG file
- **Confirmation Dialogs**: Prevent accidental overwrites

### 🪟 Window Modes
- **Normal Mode**: Standard window with full controls and transparency
- **Overlay Mode**: Frameless, click-through window for positioning over other apps

## Installation

### Prerequisites
```bash
pip install PySide6 pywin32
```

### Quick Start
1. Clone or download the files
2. Install dependencies (see above)
3. Choose your version:

**Recommended - Simple Version (Most Reliable):**
```bash
python svgresize_simple.py
```

**Full Version (With Overlay Mode):**
```bash
python svgresize.py
```

### Version Differences
- **`svgresize_simple.py`**: Reliable version with transparent window, no overlay complications
- **`svgresize.py`**: Full version with overlay mode toggle (may have stability issues)

## Usage Guide

### Loading SVG Files
1. **Drag & Drop**: Drag an SVG file from your file explorer onto the application window
2. **File Browser**: Click "Open File" and select an SVG file from the dialog

### Resizing Your SVG
1. **Proportional Scaling**: Use the scale slider to resize while maintaining aspect ratio
2. **Manual Dimensions**: Enter specific width/height values in the input boxes
3. **Aspect Ratio**: Toggle the "Lock Aspect Ratio" checkbox to enable/disable proportional scaling

### Using Overlay Mode
1. Load your SVG file and adjust it to the desired size
2. Click "Toggle Overlay Mode" to switch to transparent overlay
3. Position the window over your target application (e.g., web browser)
4. Fine-tune the size while viewing in context
5. Click "Exit Overlay Mode" to return to normal controls

### Saving Your Work
1. **Save**: Overwrites the original file (shows confirmation dialog)
2. **Save As**: Creates a new file with "_resized" suffix by default

## Technical Details

### Supported SVG Features
- Standard width/height attributes
- ViewBox-based sizing
- Preserves all SVG content and styling
- Handles various unit types (px, pt, etc.)

### File Format Support
- Input: SVG files (.svg)
- Output: SVG files with updated dimensions
- Validation: Checks for valid SVG format before processing

### Window Transparency
- Uses Qt's transparent background capabilities
- Win32 API integration for click-through functionality
- Always-on-top positioning for overlay mode

## Example Workflow

1. **Launch Application**
   ```bash
   python svgresize.py
   ```

2. **Load SVG**: Drag `logo.svg` onto the window

3. **Resize**: Use slider to scale to 150% or enter specific dimensions

4. **Test in Context**: 
   - Click "Toggle Overlay Mode"
   - Position over your web page
   - Fine-tune size as needed

5. **Save**: Click "Save As" to create `logo_resized.svg`

## Keyboard Shortcuts & Tips

- **Drag & Drop**: Works with single SVG files only
- **Real-time Updates**: All controls update each other automatically
- **Error Handling**: Clear error messages for invalid files or operations
- **Status Updates**: Bottom status bar shows current operation status

## Troubleshooting

### Common Issues
1. **Black background**: Use `svgresize_simple.py` for better transparency handling
2. **App becomes unresponsive/white**: This was an overlay mode issue - use the simple version
3. **"Could not parse SVG dimensions"**: Ensure your SVG has valid width/height or viewBox attributes
4. **Application won't start**: Check that PySide6 and pywin32 are installed

### Fixes Applied
- **Background Issues**: Fixed SVG widget styling for proper transparency
- **Overlay Mode Problems**: Created simplified version without click-through complications
- **Unresponsive Interface**: Removed problematic Win32 API calls from simple version

### File Requirements
- Valid SVG format with XML structure
- Width and height attributes OR viewBox attribute
- UTF-8 encoding recommended

### Recommended Usage
For most users, we recommend using `svgresize_simple.py` as it provides all the core functionality without the overlay mode complications that can cause the interface to become unresponsive.

## Sample SVG
A test SVG file (`test_logo.svg`) is included for demonstration purposes.

## License
This tool is provided as-is for educational and development purposes.
