# pip install pyside6
"""
Simple SVG Resizer - Reliable version without overlay mode complications
"""
import sys
import os
import xml.etree.ElementTree as ET
import re
from PySide6.QtCore import Qt
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QSlider, 
    QPushButton, QLabel, QSpinBox, QCheckBox, QFileDialog, 
    QMessageBox, QFrame, QGroupBox, QGridLayout
)
from PySide6.QtGui import QDragEnterEvent, QDropEvent

class SimpleSVGResizer(QWidget):
    def __init__(self):
        super().__init__()
        
        # Initialize variables
        self.current_svg_path = None
        self.original_width = 0
        self.original_height = 0
        self.current_width = 0
        self.current_height = 0
        self.aspect_ratio = 1.0
        self.aspect_locked = True
        self.svg_content = ""
        
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("SVG Resizer - Simple Version")
        self.resize(500, 600)
        
        # Make window semi-transparent and always on top
        self.setWindowFlags(Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Control panel
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)
        
        # SVG display area
        self.svg_widget = QSvgWidget()
        self.svg_widget.setMinimumSize(300, 300)
        self.svg_widget.setStyleSheet("""
            QSvgWidget {
                background-color: rgba(255, 255, 255, 100);
                border: 2px dashed #999;
                border-radius: 5px;
            }
        """)
        main_layout.addWidget(self.svg_widget, 1)
        
        # Status label
        self.status_label = QLabel("Drag and drop an SVG file or click 'Open File' to get started")
        self.status_label.setStyleSheet("color: #333; font-style: italic; background-color: rgba(255,255,255,150); padding: 5px; border-radius: 3px;")
        main_layout.addWidget(self.status_label)
        
        # Enable drag and drop
        self.setAcceptDrops(True)
        
    def create_control_panel(self):
        """Create the control panel with all UI controls"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Box)
        panel.setStyleSheet("QFrame { background-color: rgba(240, 240, 240, 220); border-radius: 5px; padding: 10px; }")
        
        layout = QVBoxLayout(panel)
        
        # File operations
        file_group = QGroupBox("File Operations")
        file_layout = QHBoxLayout(file_group)
        
        self.open_btn = QPushButton("Open File")
        self.open_btn.clicked.connect(self.open_file)
        file_layout.addWidget(self.open_btn)
        
        self.save_btn = QPushButton("Save")
        self.save_btn.clicked.connect(self.save_file)
        self.save_btn.setEnabled(False)
        file_layout.addWidget(self.save_btn)
        
        self.save_as_btn = QPushButton("Save As")
        self.save_as_btn.clicked.connect(self.save_as_file)
        self.save_as_btn.setEnabled(False)
        file_layout.addWidget(self.save_as_btn)
        
        layout.addWidget(file_group)
        
        # Size information
        info_group = QGroupBox("Size Information")
        info_layout = QGridLayout(info_group)
        
        info_layout.addWidget(QLabel("Original:"), 0, 0)
        self.original_size_label = QLabel("- × -")
        self.original_size_label.setStyleSheet("font-weight: bold;")
        info_layout.addWidget(self.original_size_label, 0, 1)
        
        info_layout.addWidget(QLabel("Current:"), 1, 0)
        self.current_size_label = QLabel("- × -")
        self.current_size_label.setStyleSheet("font-weight: bold; color: #0066cc;")
        info_layout.addWidget(self.current_size_label, 1, 1)
        
        layout.addWidget(info_group)
        
        # Size controls
        controls_group = QGroupBox("Size Controls")
        controls_layout = QVBoxLayout(controls_group)
        
        # Proportional scaling slider
        slider_layout = QHBoxLayout()
        slider_layout.addWidget(QLabel("Scale:"))
        self.scale_slider = QSlider(Qt.Horizontal)
        self.scale_slider.setMinimum(10)
        self.scale_slider.setMaximum(500)
        self.scale_slider.setValue(100)
        self.scale_slider.valueChanged.connect(self.on_scale_changed)
        self.scale_slider.setEnabled(False)
        slider_layout.addWidget(self.scale_slider)
        
        self.scale_label = QLabel("100%")
        self.scale_label.setMinimumWidth(50)
        slider_layout.addWidget(self.scale_label)
        controls_layout.addLayout(slider_layout)
        
        # Manual dimension inputs
        manual_layout = QHBoxLayout()
        
        manual_layout.addWidget(QLabel("Width:"))
        self.width_spinbox = QSpinBox()
        self.width_spinbox.setMinimum(1)
        self.width_spinbox.setMaximum(9999)
        self.width_spinbox.valueChanged.connect(self.on_width_changed)
        self.width_spinbox.setEnabled(False)
        manual_layout.addWidget(self.width_spinbox)
        
        manual_layout.addWidget(QLabel("Height:"))
        self.height_spinbox = QSpinBox()
        self.height_spinbox.setMinimum(1)
        self.height_spinbox.setMaximum(9999)
        self.height_spinbox.valueChanged.connect(self.on_height_changed)
        self.height_spinbox.setEnabled(False)
        manual_layout.addWidget(self.height_spinbox)
        
        # Aspect ratio lock
        self.aspect_lock_cb = QCheckBox("Lock Aspect Ratio")
        self.aspect_lock_cb.setChecked(True)
        self.aspect_lock_cb.toggled.connect(self.on_aspect_lock_toggled)
        self.aspect_lock_cb.setEnabled(False)
        manual_layout.addWidget(self.aspect_lock_cb)
        
        controls_layout.addLayout(manual_layout)
        layout.addWidget(controls_group)
        
        return panel
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """Handle drag enter events"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if len(urls) == 1 and urls[0].toLocalFile().lower().endswith('.svg'):
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()
    
    def dropEvent(self, event: QDropEvent):
        """Handle drop events"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if len(urls) == 1:
                file_path = urls[0].toLocalFile()
                if file_path.lower().endswith('.svg'):
                    self.load_svg_file(file_path)
                    event.acceptProposedAction()
                else:
                    self.show_error("Invalid file type. Please drop an SVG file.")
                    event.ignore()
            else:
                self.show_error("Please drop only one file at a time.")
                event.ignore()
    
    def open_file(self):
        """Open file dialog to select SVG file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open SVG File",
            "",
            "SVG Files (*.svg);;All Files (*)"
        )
        
        if file_path:
            self.load_svg_file(file_path)
    
    def load_svg_file(self, file_path):
        """Load and parse SVG file"""
        try:
            if not os.path.exists(file_path):
                self.show_error(f"File not found: {file_path}")
                return
            
            with open(file_path, 'r', encoding='utf-8') as f:
                self.svg_content = f.read()
            
            # Parse SVG to get dimensions
            if self.parse_svg_dimensions():
                self.current_svg_path = file_path
                self.current_width = self.original_width
                self.current_height = self.original_height
                self.aspect_ratio = self.original_width / self.original_height if self.original_height > 0 else 1.0
                
                # Update UI
                self.update_size_displays()
                self.enable_controls(True)
                self.svg_widget.load(file_path)
                
                # Reset controls
                self.scale_slider.setValue(100)
                self.width_spinbox.setValue(self.original_width)
                self.height_spinbox.setValue(self.original_height)
                
                self.status_label.setText(f"Loaded: {os.path.basename(file_path)}")
            else:
                self.show_error("Could not parse SVG dimensions. Please ensure the SVG has valid width and height attributes.")
                
        except Exception as e:
            self.show_error(f"Error loading SVG file: {str(e)}")
    
    def parse_svg_dimensions(self):
        """Parse SVG content to extract width and height"""
        try:
            # Parse XML
            root = ET.fromstring(self.svg_content)
            
            # Get width and height attributes
            width_attr = root.get('width', '')
            height_attr = root.get('height', '')
            
            if not width_attr or not height_attr:
                # Try to get from viewBox if width/height not present
                viewbox = root.get('viewBox', '')
                if viewbox:
                    parts = viewbox.split()
                    if len(parts) >= 4:
                        self.original_width = int(float(parts[2]))
                        self.original_height = int(float(parts[3]))
                        return True
                return False
            
            # Parse dimensions (remove units like px, pt, etc.)
            self.original_width = int(float(re.sub(r'[^\d.]', '', width_attr)))
            self.original_height = int(float(re.sub(r'[^\d.]', '', height_attr)))
            
            return True
            
        except Exception as e:
            print(f"Error parsing SVG: {e}")
            return False
    
    def enable_controls(self, enabled):
        """Enable or disable controls based on whether SVG is loaded"""
        self.save_btn.setEnabled(enabled)
        self.save_as_btn.setEnabled(enabled)
        self.scale_slider.setEnabled(enabled)
        self.width_spinbox.setEnabled(enabled)
        self.height_spinbox.setEnabled(enabled)
        self.aspect_lock_cb.setEnabled(enabled)
    
    def update_size_displays(self):
        """Update the size display labels"""
        self.original_size_label.setText(f"{self.original_width} × {self.original_height} px")
        self.current_size_label.setText(f"{self.current_width} × {self.current_height} px")
    
    def on_scale_changed(self, value):
        """Handle scale slider changes"""
        if not self.current_svg_path:
            return
            
        scale_factor = value / 100.0
        new_width = int(self.original_width * scale_factor)
        new_height = int(self.original_height * scale_factor)
        
        # Update spinboxes without triggering their signals
        self.width_spinbox.blockSignals(True)
        self.height_spinbox.blockSignals(True)
        self.width_spinbox.setValue(new_width)
        self.height_spinbox.setValue(new_height)
        self.width_spinbox.blockSignals(False)
        self.height_spinbox.blockSignals(False)
        
        # Update current dimensions
        self.current_width = new_width
        self.current_height = new_height
        
        # Update displays
        self.scale_label.setText(f"{value}%")
        self.update_size_displays()
        
        # Update SVG display
        self.update_svg_display()
    
    def on_width_changed(self, value):
        """Handle width spinbox changes"""
        if not self.current_svg_path:
            return
            
        self.current_width = value
        
        if self.aspect_locked:
            # Calculate new height maintaining aspect ratio
            new_height = int(value / self.aspect_ratio)
            self.height_spinbox.blockSignals(True)
            self.height_spinbox.setValue(new_height)
            self.height_spinbox.blockSignals(False)
            self.current_height = new_height
            
            # Update scale slider
            scale = int((value / self.original_width) * 100)
            self.scale_slider.blockSignals(True)
            self.scale_slider.setValue(scale)
            self.scale_slider.blockSignals(False)
            self.scale_label.setText(f"{scale}%")
        
        self.update_size_displays()
        self.update_svg_display()
    
    def on_height_changed(self, value):
        """Handle height spinbox changes"""
        if not self.current_svg_path:
            return
            
        self.current_height = value
        
        if self.aspect_locked:
            # Calculate new width maintaining aspect ratio
            new_width = int(value * self.aspect_ratio)
            self.width_spinbox.blockSignals(True)
            self.width_spinbox.setValue(new_width)
            self.width_spinbox.blockSignals(False)
            self.current_width = new_width
            
            # Update scale slider
            scale = int((value / self.original_height) * 100)
            self.scale_slider.blockSignals(True)
            self.scale_slider.setValue(scale)
            self.scale_slider.blockSignals(False)
            self.scale_label.setText(f"{scale}%")
        
        self.update_size_displays()
        self.update_svg_display()
    
    def on_aspect_lock_toggled(self, checked):
        """Handle aspect ratio lock toggle"""
        self.aspect_locked = checked
        if checked:
            # Recalculate aspect ratio based on current dimensions
            if self.current_height > 0:
                self.aspect_ratio = self.current_width / self.current_height
    
    def update_svg_display(self):
        """Update the SVG display with current dimensions"""
        if not self.current_svg_path or not self.svg_content:
            return
            
        try:
            # Create modified SVG content with new dimensions
            modified_svg = self.create_resized_svg_content()
            
            # Save to temporary file and load
            temp_path = "temp_resized.svg"
            with open(temp_path, 'w', encoding='utf-8') as f:
                f.write(modified_svg)
            
            self.svg_widget.load(temp_path)
            
            # Clean up temp file
            try:
                os.remove(temp_path)
            except:
                pass
                
        except Exception as e:
            print(f"Error updating SVG display: {e}")
    
    def create_resized_svg_content(self):
        """Create SVG content with updated dimensions"""
        try:
            root = ET.fromstring(self.svg_content)
            
            # Update width and height attributes
            root.set('width', str(self.current_width))
            root.set('height', str(self.current_height))
            
            # Update viewBox if it exists
            viewbox = root.get('viewBox')
            if viewbox:
                parts = viewbox.split()
                if len(parts) >= 4:
                    # Keep the same viewBox proportions but update the size
                    parts[2] = str(self.current_width)
                    parts[3] = str(self.current_height)
                    root.set('viewBox', ' '.join(parts))
            
            # Convert back to string
            return ET.tostring(root, encoding='unicode')
            
        except Exception as e:
            print(f"Error creating resized SVG: {e}")
            return self.svg_content
    
    def save_file(self):
        """Save the resized SVG to the original file"""
        if not self.current_svg_path:
            return
            
        reply = QMessageBox.question(
            self,
            "Confirm Save",
            f"Are you sure you want to overwrite the original file?\n{os.path.basename(self.current_svg_path)}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.save_svg_to_file(self.current_svg_path)
    
    def save_as_file(self):
        """Save the resized SVG to a new file"""
        if not self.current_svg_path:
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save SVG As",
            os.path.splitext(self.current_svg_path)[0] + "_resized.svg",
            "SVG Files (*.svg);;All Files (*)"
        )
        
        if file_path:
            self.save_svg_to_file(file_path)
    
    def save_svg_to_file(self, file_path):
        """Save the resized SVG content to a file"""
        try:
            resized_content = self.create_resized_svg_content()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(resized_content)
            
            self.status_label.setText(f"Saved: {os.path.basename(file_path)}")
            
            # Show success message
            QMessageBox.information(
                self,
                "Save Successful",
                f"SVG file saved successfully!\n{os.path.basename(file_path)}"
            )
            
        except Exception as e:
            self.show_error(f"Error saving file: {str(e)}")
    
    def show_error(self, message):
        """Show error message to user"""
        QMessageBox.critical(self, "Error", message)
        self.status_label.setText(f"Error: {message}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Create and show the main window
    window = SimpleSVGResizer()
    window.show()
    
    sys.exit(app.exec())
